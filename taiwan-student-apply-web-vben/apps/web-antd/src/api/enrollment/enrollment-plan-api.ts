/*
 * 招生计划管理API
 *
 * @Author: Kerwin
 * @Date: 2024-12-23
 * @Copyright 台湾学生申请系统
 */

import { requestClient } from '../request';

export interface EnrollmentPlanQueryParams {
  pageNum: number;
  pageSize: number;
  year?: number;
  universityName?: string;
  majorName?: string;
  subjectType?: number;
  province?: string;
}

export interface EnrollmentPlanVO {
  enrollmentPlanId: number;
  year: number;
  universityName: string;
  majorName: string;
  subjectType: number;
  subjectTypeName: string;
  province: string;
  plannedNumber: number;
  actualNumber?: number;
  tuitionFee?: number;
  remarks?: string;
  createTime: string;
  updateTime: string;
}

export interface EnrollmentPlanAddForm {
  year: number;
  universityName: string;
  majorName: string;
  subjectType: number;
  province: string;
  plannedNumber: number;
  actualNumber?: number;
  tuitionFee?: number;
  remarks?: string;
}

export interface EnrollmentPlanUpdateForm extends EnrollmentPlanAddForm {
  enrollmentPlanId: number;
}

export interface YearStatistics {
  year: number;
  count: number;
}

export interface CopyFromPreviousYearForm {
  fromYear: number;
  toYear: number;
  universityNames?: string[];
}

export const enrollmentPlanApi = {
  /**
   * 分页查询招生计划
   */
  queryPage: (params: EnrollmentPlanQueryParams) => {
    return requestClient.post<{
      list: EnrollmentPlanVO[];
      total: number;
    }>('/enrollmentPlan/queryPage', params);
  },

  /**
   * 新增招生计划
   */
  add: (data: EnrollmentPlanAddForm) => {
    return requestClient.post('/enrollmentPlan/add', data);
  },

  /**
   * 更新招生计划
   */
  update: (data: EnrollmentPlanUpdateForm) => {
    return requestClient.post('/enrollmentPlan/update', data);
  },

  /**
   * 删除招生计划
   */
  delete: (enrollmentPlanId: number) => {
    return requestClient.post(`/enrollmentPlan/delete/${enrollmentPlanId}`);
  },

  /**
   * 批量删除招生计划
   */
  batchDelete: (idList: number[]) => {
    return requestClient.post('/enrollmentPlan/batchDelete', idList);
  },

  /**
   * 查询招生计划详情
   */
  getDetail: (enrollmentPlanId: number) => {
    return requestClient.get<EnrollmentPlanVO>(`/enrollmentPlan/detail/${enrollmentPlanId}`);
  },

  /**
   * 查询所有年度列表
   */
  queryYearList: () => {
    return requestClient.get<number[]>('/enrollmentPlan/queryYearList');
  },

  /**
   * 根据年度统计招生计划数量
   */
  countByYear: () => {
    return requestClient.get<YearStatistics[]>('/enrollmentPlan/countByYear');
  },

  /**
   * 年度复制
   */
  copyFromPreviousYear: (data: CopyFromPreviousYearForm) => {
    return requestClient.post('/enrollmentPlan/copyFromPreviousYear', data);
  },

  /**
   * 导出Excel
   */
  exportExcel: (params: EnrollmentPlanQueryParams) => {
    return requestClient.download('/enrollmentPlan/exportExcel', { params });
  },

  /**
   * 下载导入模板
   */
  downloadTemplate: () => {
    return requestClient.download('/enrollmentPlan/downloadTemplate');
  },

  /**
   * 导入Excel
   */
  importExcel: (formData: FormData) => {
    return requestClient.post('/enrollmentPlan/importExcel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};
