/*
 * 登录相关API
 *
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-23
 * @Copyright 台湾学生申请系统
 */

import { requestClient } from '../request';

export interface LoginParams {
  loginName: string;
  password: string;
  captchaCode?: string;
  captchaUuid?: string;
  emailCode?: string;
}

export interface LoginResult {
  accessToken: string;
  refreshToken?: string;
  userInfo: {
    userId: number;
    userName: string;
    actualName: string;
    phone?: string;
    email?: string;
    avatar?: string;
    departmentId?: number;
    departmentName?: string;
  };
}

export interface CaptchaResult {
  captchaUuid: string;
  captchaImage: string;
}

export interface LoginInfoResult {
  userId: number;
  userName: string;
  actualName: string;
  phone?: string;
  email?: string;
  avatar?: string;
  departmentId?: number;
  departmentName?: string;
  roleList: Array<{
    roleId: number;
    roleName: string;
    roleCode: string;
  }>;
  menuList: Array<{
    menuId: number;
    menuName: string;
    menuType: number;
    path?: string;
    component?: string;
    icon?: string;
    parentId?: number;
    sort: number;
    perms?: string;
  }>;
}

export const loginApi = {
  /**
   * 用户登录
   */
  login: (params: LoginParams) => {
    return requestClient.post<LoginResult>('/login', params);
  },

  /**
   * 退出登录
   */
  logout: () => {
    return requestClient.get('/login/logout');
  },

  /**
   * 获取验证码
   */
  getCaptcha: () => {
    return requestClient.get<CaptchaResult>('/login/getCaptcha');
  },

  /**
   * 获取登录信息
   */
  getLoginInfo: () => {
    return requestClient.get<LoginInfoResult>('/login/getLoginInfo');
  },

  /**
   * 发送邮箱登录验证码
   */
  sendLoginEmailCode: (loginName: string) => {
    return requestClient.get(`/login/sendEmailCode/${loginName}`);
  },

  /**
   * 获取双因子登录标识
   */
  getTwoFactorLoginFlag: () => {
    return requestClient.get<boolean>('/login/getTwoFactorLoginFlag');
  },

  /**
   * 检查是否需要图形验证码
   */
  checkNeedCaptcha: (loginName: string) => {
    return requestClient.get<boolean>(`/login/checkNeedCaptcha/${loginName}`);
  },
};
