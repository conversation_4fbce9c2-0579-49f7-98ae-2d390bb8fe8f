/*
 * 字符串相关操作
 *
 * @Author: Kerwin
 * @Date: 2024-12-23
 * @Copyright 台湾学生申请系统
 */

/**
 * 转为小写中划线
 * @param str 输入字符串
 * @returns 转换后的字符串
 */
export function convertLowerHyphen(str: string | null | undefined): string {
  if (!str) {
    return '';
  }

  return str
    .replace(/([A-Z])/g, '-$1')
    .toLowerCase()
    .substring(1);
}

/**
 * 转为大驼峰（PascalCase）
 * @param str 输入字符串
 * @returns 转换后的字符串
 */
export function convertUpperCamel(str: string | null | undefined): string {
  if (!str) {
    return '';
  }

  str = str.replace(/_(\w)/g, (_, letter) => letter.toUpperCase());
  // 首字母大写
  return str[0].toUpperCase() + str.substring(1);
}

/**
 * 转为小驼峰（camelCase）
 * @param str 输入字符串
 * @returns 转换后的字符串
 */
export function convertLowerCamel(str: string | null | undefined): string {
  if (!str) {
    return '';
  }

  return str.replace(/_(\w)/g, (_, letter) => letter.toUpperCase());
}

/**
 * 判断字符串是否为空
 * @param str 输入字符串
 * @returns 是否为空
 */
export function isEmpty(str: string | null | undefined): boolean {
  return !str || str.trim().length === 0;
}

/**
 * 判断字符串是否不为空
 * @param str 输入字符串
 * @returns 是否不为空
 */
export function isNotEmpty(str: string | null | undefined): boolean {
  return !isEmpty(str);
}

/**
 * 截取字符串
 * @param str 输入字符串
 * @param length 截取长度
 * @param suffix 后缀，默认为 '...'
 * @returns 截取后的字符串
 */
export function truncate(str: string | null | undefined, length: number, suffix = '...'): string {
  if (!str) return '';
  if (str.length <= length) return str;
  return str.substring(0, length) + suffix;
}

/**
 * 首字母大写
 * @param str 输入字符串
 * @returns 首字母大写的字符串
 */
export function capitalize(str: string | null | undefined): string {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * 移除字符串两端空格
 * @param str 输入字符串
 * @returns 移除空格后的字符串
 */
export function trim(str: string | null | undefined): string {
  return str ? str.trim() : '';
}
