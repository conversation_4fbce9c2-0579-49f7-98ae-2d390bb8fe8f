/*
 * 日期工具函数
 *
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-12-23
 */

/**
 * 格式化日期时间
 * @param date 日期
 * @param format 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(date: string | Date | null | undefined, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * 格式化日期
 * @param date 日期
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: string | Date | null | undefined): string {
  return formatDateTime(date, 'YYYY-MM-DD');
}

/**
 * 格式化时间
 * @param date 日期
 * @returns 格式化后的时间字符串
 */
export function formatTime(date: string | Date | null | undefined): string {
  return formatDateTime(date, 'HH:mm:ss');
}

/**
 * 获取当前时间戳
 * @returns 当前时间戳
 */
export function getCurrentTimestamp(): number {
  return Date.now();
}

/**
 * 获取当前日期字符串
 * @param format 格式，默认 'YYYY-MM-DD'
 * @returns 当前日期字符串
 */
export function getCurrentDate(format = 'YYYY-MM-DD'): string {
  return formatDateTime(new Date(), format);
}

/**
 * 获取当前日期时间字符串
 * @param format 格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns 当前日期时间字符串
 */
export function getCurrentDateTime(format = 'YYYY-MM-DD HH:mm:ss'): string {
  return formatDateTime(new Date(), format);
}
