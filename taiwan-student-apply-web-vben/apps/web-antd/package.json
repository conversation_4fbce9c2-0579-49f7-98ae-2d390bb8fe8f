{"name": "@vben/web-antd", "version": "1.0.0", "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "axios": "catalog:", "clipboard": "catalog:", "crypto-js": "catalog:", "dayjs": "catalog:", "decimal.js": "catalog:", "echarts": "catalog:", "lodash": "catalog:", "mitt": "catalog:", "pinia": "catalog:", "sm-crypto": "catalog:", "sortablejs": "catalog:", "ua-parser-js": "catalog:", "uuid": "catalog:", "vue": "catalog:", "vue-i18n": "catalog:", "vue-router": "catalog:", "vue3-json-viewer": "catalog:"}}