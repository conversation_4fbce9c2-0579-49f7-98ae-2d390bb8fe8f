import { defineConfig } from '@vben/vite-config';

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      server: {
        host: '0.0.0.0',
        port: 8081,
        proxy: {
          '/api': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, ''),
            // 后端API地址
            target: 'http://localhost:1024',
            ws: true,
          },
        },
      },
      resolve: {
        alias: [
          // 兼容原项目的路径别名
          {
            find: /\/@\//,
            replacement: '/src/',
          },
        ],
      },
    },
  };
});
