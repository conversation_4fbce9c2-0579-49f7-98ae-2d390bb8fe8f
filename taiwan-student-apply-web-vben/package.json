{"name": "taiwan-student-apply-admin", "version": "1.0.0", "private": true, "keywords": ["taiwan", "student", "apply", "admin", "vue", "vue admin", "vben admin", "vue3"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>"}, "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 turbo build", "build:analyze": "turbo build:analyze", "build:antd": "pnpm run build --filter=@vben/web-antd", "build:prod": "pnpm run build --filter=@vben/web-antd", "build:test": "pnpm run build --filter=@vben/web-antd build:test", "check": "pnpm run check:circular && pnpm run check:dep && pnpm run check:type", "check:circular": "vsh check-circular", "check:dep": "vsh check-dep", "check:type": "turbo run typecheck", "clean": "node ./scripts/clean.mjs", "dev": "pnpm -F @vben/web-antd run dev", "format": "vsh lint --format", "lint": "vsh lint", "postinstall": "pnpm -r run stub --if-present", "preinstall": "npx only-allow pnpm", "preview": "pnpm -F @vben/web-antd run preview", "reinstall": "pnpm clean --del-lock && pnpm install"}, "devDependencies": {"@types/node": "catalog:", "@vben/eslint-config": "workspace:*", "@vben/prettier-config": "workspace:*", "@vben/stylelint-config": "workspace:*", "@vben/tailwind-config": "workspace:*", "@vben/tsconfig": "workspace:*", "@vben/turbo-run": "workspace:*", "@vben/vite-config": "workspace:*", "@vben/vsh": "workspace:*", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "autoprefixer": "catalog:", "cross-env": "catalog:", "rimraf": "catalog:", "tailwindcss": "catalog:", "turbo": "catalog:", "typescript": "catalog:", "unbuild": "catalog:", "vite": "catalog:", "vue": "catalog:", "vue-tsc": "catalog:"}, "engines": {"node": ">=20.10.0", "pnpm": ">=9.12.0"}, "packageManager": "pnpm@10.10.0", "pnpm": {"peerDependencyRules": {"allowedVersions": {"eslint": "*"}}, "overrides": {"@ast-grep/napi": "catalog:", "@ctrl/tinycolor": "catalog:", "clsx": "catalog:", "esbuild": "0.25.3", "pinia": "catalog:", "vue": "catalog:"}, "neverBuiltDependencies": ["canvas", "node-gyp"]}}